# ARBIG Web指挥中轴 前端界面设计文档

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-01-04
- **设计目标**: 现代化、直观、响应式的Web指挥中轴界面

## 🎨 设计原则

### 1. 用户体验原则
- **直观易用**: 操作逻辑清晰，新用户快速上手
- **信息层次**: 重要信息突出显示，次要信息适当弱化
- **响应式设计**: 支持桌面、平板、手机多端访问
- **实时反馈**: 操作结果即时反馈，状态变化实时更新

### 2. 视觉设计原则
- **专业性**: 体现金融交易系统的专业性和可靠性
- **现代感**: 采用现代化的设计语言和交互方式
- **一致性**: 统一的色彩、字体、间距、组件风格
- **可读性**: 确保在各种环境下的信息可读性

## 🎯 整体布局设计

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│  Header: ARBIG量化交易系统 - 指挥中轴    [用户] [设置] [帮助] │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│ │             │ │                                         │ │
│ │  系统控制   │ │              实时行情                   │ │
│ │    面板     │ │                                         │ │
│ │             │ │                                         │ │
│ └─────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────┐ ┌─────────────────────────┐ │
│ │         账户资金            │ │        持仓明细         │ │
│ │                             │ │                         │ │
│ └─────────────────────────────┘ └─────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                    策略管理中心                         │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Footer: 系统状态 | 连接状态 | 版本信息                     │
└─────────────────────────────────────────────────────────────┘
```

### 响应式布局
- **桌面端 (≥1200px)**: 4列网格布局
- **平板端 (768px-1199px)**: 2列网格布局  
- **手机端 (<768px)**: 单列堆叠布局

## 🎮 系统控制面板

### 设计目标
提供系统级操作的统一入口，状态一目了然

### 界面设计
```
┌─────────────────┐
│   系统控制面板   │
├─────────────────┤
│ 🟢 行情服务     │ ← 绿色表示运行中
│ 🟢 账户服务     │
│ 🟡 风控服务     │ ← 黄色表示警告状态
│ 🔴 交易服务     │ ← 红色表示停止/错误
├─────────────────┤
│ 运行模式        │
│ [完整交易模式]  │ ← 当前模式高亮显示
│ [ 监控模式 ]    │
│ [ 行情模式 ]    │
├─────────────────┤
│ 系统操作        │
│ [🔄 重启系统]   │
│ [⏸️ 暂停交易]   │
│ [🛑 紧急停止]   │ ← 危险操作红色显示
│ [💰 紧急平仓]   │
└─────────────────┘
```

### 交互设计
- **服务状态**: 点击可查看详细信息和操作选项
- **模式切换**: 下拉选择或单选按钮
- **危险操作**: 需要二次确认，显示确认对话框
- **实时更新**: 状态变化时自动刷新，无需手动刷新

### 状态指示器
```css
.service-status {
  .running { color: #52c41a; }    /* 绿色 - 运行中 */
  .warning { color: #faad14; }    /* 黄色 - 警告 */
  .error { color: #f5222d; }      /* 红色 - 错误 */
  .stopped { color: #8c8c8c; }    /* 灰色 - 停止 */
}
```

## 📊 实时行情展示

### 设计目标
提供清晰、实时的市场数据展示，支持多合约监控

### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│                      实时行情                               │
├─────────────────────────────────────────────────────────────┤
│ 合约选择: [AU2509] [AU2512] [AU2601] [+ 添加]              │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   K线图表区域                           │ │
│ │  ┌─────────────────────────────────────────────────────┐ │ │
│ │  │                                                     │ │ │
│ │  │              TradingView 图表                       │ │ │
│ │  │                                                     │ │ │
│ │  └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ AU2509: 485.50 ↗ +2.30 (+0.48%) | 成交: 12,580 | 持仓: 45,230 │
│ 最高: 487.20 | 最低: 483.10 | 开盘: 483.20 | 昨收: 483.20    │
└─────────────────────────────────────────────────────────────┘
```

### 功能特性
- **多合约切换**: 标签页形式，支持添加/删除监控合约
- **图表工具**: 集成TradingView或自研图表组件
- **实时更新**: WebSocket推送，价格变化高亮显示
- **技术指标**: 支持常用技术指标叠加显示

### 价格显示规则
```css
.price-change {
  .positive { color: #f5222d; }   /* 红色 - 上涨 */
  .negative { color: #52c41a; }   /* 绿色 - 下跌 */
  .neutral { color: #8c8c8c; }    /* 灰色 - 无变化 */
}
```

## 💰 账户资金面板

### 设计目标
清晰展示账户资金状况，突出关键指标

### 界面设计
```
┌─────────────────────────────┐
│         账户资金            │
├─────────────────────────────┤
│ 总资产    ¥1,000,000       │ ← 大字体显示
│ 可用资金  ¥850,000         │
│ 占用保证金 ¥150,000        │
│ 冻结资金  ¥0               │
├─────────────────────────────┤
│ 浮动盈亏  +¥25,000 📈      │ ← 盈利绿色，亏损红色
│ 今日盈亏  +¥5,000          │
│ 手续费    ¥120.50          │
├─────────────────────────────┤
│ 风险度    65% ████░░░░      │ ← 进度条显示
│ 更新时间  18:00:25         │
└─────────────────────────────┘
```

### 数据展示规则
- **金额格式**: 千分位分隔符，保留2位小数
- **盈亏颜色**: 盈利显示绿色，亏损显示红色
- **风险度**: 进度条形式，超过80%显示警告色
- **实时更新**: 账户变动时自动刷新

## 📋 持仓明细面板

### 设计目标
详细展示持仓信息，支持快速操作

### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│                        持仓明细                             │
├─────────────────────────────────────────────────────────────┤
│ 合约    方向  数量  均价    现价    盈亏      保证金   操作  │
├─────────────────────────────────────────────────────────────┤
│ AU2509  多   5手   483.20  485.50  +11,500  120,000  [平仓] │
│ AU2512  空   2手   486.80  485.20  +3,200   48,000   [平仓] │
├─────────────────────────────────────────────────────────────┤
│ 合计持仓: 7手 | 总盈亏: +14,700 | 总保证金: 168,000        │
└─────────────────────────────────────────────────────────────┘
```

### 交互功能
- **排序**: 点击列标题可按该列排序
- **筛选**: 支持按合约、方向筛选
- **快速平仓**: 点击平仓按钮，确认后执行
- **详情查看**: 点击行可查看持仓详细信息

## 🎯 策略管理中心

### 设计目标
提供完整的策略管理功能，直观展示策略状态

### 界面设计
```
┌─────────────────────────────────────────────────────────────┐
│                      策略管理中心                           │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   策略选择      │ │   策略监控      │ │   策略分析      │ │
│ │                 │ │                 │ │                 │ │
│ │ ○ 趋势策略      │ │ 当前策略:       │ │ 今日收益:       │ │
│ │ ● 套利策略      │ │ 套利策略        │ │ +2.3% 📈       │ │
│ │ ○ 高频策略      │ │                 │ │                 │ │
│ │ ○ 无策略        │ │ 运行时间:       │ │ 最大回撤:       │ │
│ │                 │ │ 2h 30m 15s      │ │ -1.2%           │ │
│ │ [切换策略]      │ │                 │ │                 │ │
│ │ [策略参数]      │ │ 信号数: 15      │ │ 胜率: 68.5%     │ │
│ │                 │ │ 成交数: 8       │ │ 夏普: 1.85      │ │
│ │                 │ │                 │ │                 │ │
│ │                 │ │ [暂停策略]      │ │ [详细报告]      │ │
│ │                 │ │ [重启策略]      │ │ [历史回测]      │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 策略切换流程
1. **选择新策略** → 显示策略信息和配置选项
2. **确认切换** → 显示切换确认对话框
3. **执行切换** → 显示切换进度和状态
4. **完成切换** → 更新界面显示新策略信息

## 🔔 通知和提醒

### 设计目标
及时提醒用户重要事件和状态变化

### 通知类型
- **系统通知**: 服务启停、连接状态变化
- **交易通知**: 订单成交、策略信号
- **风控通知**: 风险警告、限制触发
- **错误通知**: 系统错误、操作失败

### 显示方式
```
┌─────────────────────────────────────┐
│ 🔔 通知中心                         │
├─────────────────────────────────────┤
│ ⚠️  风险提醒: 持仓风险度达到75%      │
│     2025-01-04 18:00:25            │
├─────────────────────────────────────┤
│ ✅ 交易通知: AU2509多单成交         │
│     成交价: 485.50, 数量: 2手       │
│     2025-01-04 17:58:12            │
├─────────────────────────────────────┤
│ 🔄 系统通知: 策略已切换至趋势策略    │
│     2025-01-04 17:55:30            │
└─────────────────────────────────────┘
```

## 📱 移动端适配

### 设计原则
- **触摸友好**: 按钮大小适合手指操作
- **信息精简**: 突出最重要的信息
- **手势支持**: 支持滑动、缩放等手势操作

### 移动端布局
```
┌─────────────────┐
│ ARBIG 指挥中轴  │ ← 顶部导航
├─────────────────┤
│ 🟢 系统状态     │ ← 折叠式状态面板
│ [展开详情]      │
├─────────────────┤
│ AU2509: 485.50  │ ← 简化行情显示
│ ↗ +2.30 (+0.48%)│
├─────────────────┤
│ 总资产: 100万   │ ← 关键账户信息
│ 可用: 85万      │
│ 盈亏: +2.5万    │
├─────────────────┤
│ 当前策略:       │ ← 策略状态
│ 套利策略 🟢     │
│ [切换] [暂停]   │
├─────────────────┤
│ [详细] [设置]   │ ← 底部操作栏
└─────────────────┘
```

## 🎨 视觉设计规范

### 色彩方案
```css
:root {
  /* 主色调 */
  --primary-color: #1890ff;      /* 蓝色 - 主要操作 */
  --success-color: #52c41a;      /* 绿色 - 成功/上涨 */
  --warning-color: #faad14;      /* 黄色 - 警告 */
  --error-color: #f5222d;        /* 红色 - 错误/下跌 */
  
  /* 背景色 */
  --bg-primary: #ffffff;         /* 主背景 */
  --bg-secondary: #f5f5f5;       /* 次背景 */
  --bg-dark: #001529;            /* 深色背景 */
  
  /* 文字色 */
  --text-primary: #262626;       /* 主要文字 */
  --text-secondary: #8c8c8c;     /* 次要文字 */
  --text-disabled: #bfbfbf;      /* 禁用文字 */
}
```

### 字体规范
- **主字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- **数字字体**: 'SF Mono', Monaco, 'Cascadia Code', monospace
- **字号**: 12px(小), 14px(正文), 16px(标题), 20px(大标题)

### 间距规范
- **基础间距**: 8px的倍数 (8px, 16px, 24px, 32px)
- **组件内边距**: 16px
- **组件间距**: 24px
- **页面边距**: 32px

## 🔧 技术实现

### 前端技术栈
- **框架**: Vue.js 3 + TypeScript
- **UI组件**: Ant Design Vue
- **图表**: TradingView Charting Library
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **WebSocket**: 原生WebSocket API

### 构建工具
- **构建工具**: Vite
- **代码规范**: ESLint + Prettier
- **CSS预处理**: Sass/SCSS
- **包管理**: npm/yarn

---

**下一步**: 基于此设计文档，开始前端界面的开发实现。
