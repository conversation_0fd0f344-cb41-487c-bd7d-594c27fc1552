# 开仓模式管理系统

## 概述

ARBIG量化交易系统现在支持多种开仓模式，允许用户根据不同的市场环境和风险偏好选择最适合的仓位管理策略。

## 支持的开仓模式

### 1. 固定手数模式 (Fixed Position)
- **风险等级**: 🟢 低风险
- **适用场景**: 新手交易者、保守策略
- **计算方式**: 每次开仓使用固定的手数
- **优点**:
  - 风险可控，易于理解
  - 适合稳定盈利的策略
  - 不受账户资金变化影响
  - 适合新手交易者
- **参数**:
  - `position_size`: 基础开仓手数
  - `position_multiplier`: 仓位倍数

### 2. 风险比例模式 (Risk-Based Position)
- **风险等级**: 🟡 中等风险
- **适用场景**: 长期资金增长、动态仓位管理
- **计算方式**: 根据账户资金的固定比例来计算开仓手数
- **优点**:
  - 仓位随资金规模动态调整
  - 有效控制单笔交易风险
  - 适合长期资金增长
  - 需要设置合理的风险比例
- **参数**:
  - `risk_factor`: 风险系数 (建议0.01-0.05)
  - `position_multiplier`: 仓位倍数
- **计算公式**:
  ```
  风险金额 = 账户余额 × 风险系数
  每手风险 = 价格 × 止损比例 × 每手数量
  开仓手数 = 风险金额 ÷ 每手风险 × 仓位倍数
  ```

### 3. 凯利公式模式 (Kelly Formula)
- **风险等级**: 🟡 中等风险
- **适用场景**: 有历史数据的成熟策略
- **计算方式**: 基于历史胜率和盈亏比计算最优仓位
- **优点**:
  - 理论最优仓位计算
  - 需要准确的胜率统计
  - 适合有历史数据的策略
  - 可能产生较大仓位波动
- **参数**:
  - `win_rate`: 胜率 (0-1)
  - `avg_win`: 平均盈利比例
  - `avg_loss`: 平均亏损比例
  - `position_multiplier`: 仓位倍数
- **计算公式**:
  ```
  凯利比例 = (盈亏比 × 胜率 - 败率) ÷ 盈亏比
  开仓手数 = 凯利比例 × 资金比例 × 仓位倍数
  ```

### 4. 马丁格尔模式 (Martingale)
- **风险等级**: 🔴 高风险
- **适用场景**: 充足资金、高风险偏好
- **计算方式**: 亏损后加倍下注，盈利后回到基础仓位
- **优点**:
  - 亏损后自动加仓
  - 理论上必然盈利
  - 需要充足的资金支持
  - 极高风险，谨慎使用
- **参数**:
  - `position_size`: 基础开仓手数
  - `martingale_multiplier`: 马丁格尔倍数 (建议2.0)
  - `position_multiplier`: 仓位倍数
- **计算公式**:
  ```
  开仓手数 = 基础手数 × 马丁格尔倍数^连续亏损次数 × 仓位倍数
  ```

## 配置参数

### 基础参数
```yaml
# 开仓参数
position_size: 1           # 基础开仓手数
max_position: 5            # 最大持仓手数
risk_factor: 0.02          # 风险系数
add_interval: 50           # 加仓间隔(点)
position_mode: "fixed"     # 开仓模式
position_multiplier: 1.0   # 仓位倍数
```

### 凯利公式参数
```yaml
# 凯利公式参数
win_rate: 0.6              # 胜率
avg_win: 1.5               # 平均盈利比例
avg_loss: 1.0              # 平均亏损比例
```

### 马丁格尔参数
```yaml
# 马丁格尔参数
martingale_multiplier: 2.0 # 马丁格尔倍数
```

## API接口

### 获取策略参数
```http
GET /api/v1/strategies/{strategy_name}/params
```

### 更新策略参数
```http
POST /api/v1/strategies/{strategy_name}/params
Content-Type: application/json

{
  "position_mode": "risk_based",
  "position_size": 2,
  "risk_factor": 0.03,
  "position_multiplier": 1.2
}
```

### 运行历史回测
```http
POST /api/v1/strategies/{strategy_name}/backtest
Content-Type: application/json

{
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "initial_capital": 100000
}
```

## Web界面功能

### 策略监控页面
- **实时参数显示**: 显示当前策略的所有参数
- **参数编辑**: 可拖动的模态框，支持实时参数修改
- **模式说明**: 每种开仓模式的详细说明和风险等级
- **参数测试**: 测试不同开仓模式的计算结果
- **历史回测**: 在线回测功能，验证参数效果

### 触发记录
- **实时记录**: 显示策略触发的详细条件和执行结果
- **参数信息**: 记录中包含使用的开仓参数
- **状态标识**: 成功✅、警告⚠️、错误❌状态

### 性能监控
- **实时更新**: 策略性能数据每10秒更新
- **关键指标**: 总盈亏、今日盈亏、持仓、胜率等
- **动态显示**: 数据变化时的颜色和动画效果

## 风险控制

### 仓位限制
- **最大持仓**: 限制策略的最大持仓手数
- **加仓间隔**: 防止频繁加仓的价格间隔控制
- **风险系数**: 单笔交易风险占总资金的比例限制

### 模式选择建议
- **新手用户**: 推荐使用"固定手数"模式
- **有经验用户**: 可以尝试"风险比例"模式
- **专业交易者**: 可以使用"凯利公式"优化仓位
- **高风险偏好**: 谨慎考虑"马丁格尔"模式

## 实现细节

### 核心模块
- `core/position_manager.py`: 仓位管理器，实现各种开仓模式算法
- `strategies/shfe_quant.py`: 策略实现，集成仓位管理器
- `web_admin/api/routers/strategies.py`: API路由，提供参数管理接口

### 前端功能
- 模态框拖动功能
- 实时参数加载和更新
- 开仓模式测试计算器
- 参数说明和风险提示

## 使用示例

### 设置风险比例模式
```python
# 通过API更新参数
params = {
    "position_mode": "risk_based",
    "risk_factor": 0.02,  # 2%风险
    "position_multiplier": 1.0
}
```

### 设置凯利公式模式
```python
# 基于历史数据设置凯利参数
params = {
    "position_mode": "kelly",
    "win_rate": 0.65,     # 65%胜率
    "avg_win": 1.8,       # 平均盈利1.8倍
    "avg_loss": 1.0,      # 平均亏损1倍
    "position_multiplier": 0.8  # 保守一些
}
```

## 注意事项

1. **参数验证**: 所有参数都有合理范围限制
2. **实时生效**: 参数修改后立即生效，影响后续交易
3. **回测验证**: 建议修改参数后先进行历史回测
4. **风险控制**: 高风险模式需要充足的资金支持
5. **监控重要**: 使用新模式时需要密切监控策略表现

## 未来扩展

- 支持更多开仓模式（如波动率调整、技术指标权重等）
- 增加机器学习优化的动态仓位调整
- 实现多策略组合的仓位分配
- 添加更详细的风险分析和预警功能
