/**
 * 全局样式
 */

// CSS变量定义
:root {
  // 主色调
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;

  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-dark: #001529;
  --bg-card: #ffffff;

  // 文字色
  --text-primary: #262626;
  --text-secondary: #8c8c8c;
  --text-disabled: #bfbfbf;
  --text-white: #ffffff;

  // 边框色
  --border-color: #d9d9d9;
  --border-light: #f0f0f0;

  // 阴影
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 12px rgba(0, 0, 0, 0.15);

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  // 圆角
  --border-radius: 6px;
  --border-radius-lg: 8px;
}

// 全局重置
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

#app {
  height: 100vh;
  overflow: hidden;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.bg-success {
  background-color: var(--success-color);
}

.bg-warning {
  background-color: var(--warning-color);
}

.bg-error {
  background-color: var(--error-color);
}

// 状态指示器
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);

  &.running {
    background-color: var(--success-color);
  }

  &.stopped {
    background-color: var(--text-secondary);
  }

  &.error {
    background-color: var(--error-color);
  }

  &.warning {
    background-color: var(--warning-color);
  }
}

// 卡片样式
.card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);

  &:hover {
    box-shadow: var(--shadow-hover);
  }

  .card-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
  }

  .card-content {
    color: var(--text-primary);
  }
}

// 数字显示
.number-display {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-weight: 600;

  &.large {
    font-size: 24px;
  }

  &.medium {
    font-size: 18px;
  }

  &.small {
    font-size: 14px;
  }

  &.positive {
    color: var(--success-color);
  }

  &.negative {
    color: var(--error-color);
  }

  &.neutral {
    color: var(--text-primary);
  }
}

// 价格变化样式
.price-change {
  &.up {
    color: var(--error-color); // 中国股市红涨绿跌
  }

  &.down {
    color: var(--success-color);
  }

  &.neutral {
    color: var(--text-secondary);
  }
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--text-secondary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }

  .number-display {
    &.large {
      font-size: 20px;
    }

    &.medium {
      font-size: 16px;
    }
  }
}

// Ant Design 组件自定义
.ant-layout {
  height: 100vh;
}

.ant-layout-header {
  background: var(--bg-dark);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ant-layout-content {
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.ant-layout-sider {
  background: var(--bg-card);
}

// 自定义按钮样式
.danger-button {
  background-color: var(--error-color);
  border-color: var(--error-color);
  color: var(--text-white);

  &:hover {
    background-color: #ff4d4f;
    border-color: #ff4d4f;
  }
}

// 动画
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}
