<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略监控 - ARBIG量化交易系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .back-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            position: relative;
            overflow: hidden;
        }

        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .back-btn:hover::before {
            left: 100%;
        }

        .strategy-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .info-item:hover::before {
            transform: scaleX(1);
        }

        .info-label {
            font-size: 13px;
            color: #7f8c8d;
            margin-bottom: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.2;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.2);
        }

        .panel:hover::before {
            opacity: 1;
        }

        .panel h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 22px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-running { background: #27ae60; }
        .status-stopped { background: #e74c3c; }
        .status-paused { background: #f39c12; }

        .control-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-start {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-stop {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .btn-pause {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .btn:hover::before {
            left: 100%;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 25px 20px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-value {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-label {
            font-size: 13px;
            color: #7f8c8d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .neutral { color: #3498db; }

        .log-container {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: #ecf0f1;
            border-radius: 16px;
            padding: 20px;
            height: 350px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .log-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
            background-size: 300% 100%;
            animation: logGradient 4s ease infinite;
        }

        @keyframes logGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .log-container::-webkit-scrollbar {
            width: 8px;
        }

        .log-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .log-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .log-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 6px 0;
            border-left: 3px solid transparent;
            padding-left: 10px;
            transition: all 0.2s ease;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.05);
            border-left-color: #3498db;
        }

        .log-timestamp {
            color: #95a5a6;
            margin-right: 12px;
            font-weight: 500;
        }

        .log-level-INFO {
            color: #3498db;
            text-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }
        .log-level-WARN {
            color: #f39c12;
            text-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
        }
        .log-level-ERROR {
            color: #e74c3c;
            text-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
        }
        .log-level-DEBUG {
            color: #95a5a6;
            text-shadow: 0 0 5px rgba(149, 165, 166, 0.3);
        }

        .positions-table, .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .positions-table th, .positions-table td,
        .orders-table th, .orders-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background-color 0.2s ease;
        }

        .positions-table th, .orders-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            font-weight: 600;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .positions-table tbody tr, .orders-table tbody tr {
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
        }

        .positions-table tbody tr:hover, .orders-table tbody tr:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.01);
        }

        .positions-table tbody tr:nth-child(even),
        .orders-table tbody tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.9);
        }

        /* 策略触发记录样式 */
        .trigger-container {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px 0;
        }

        .trigger-container::-webkit-scrollbar {
            width: 6px;
        }

        .trigger-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .trigger-container::-webkit-scrollbar-thumb {
            background: rgba(46, 204, 113, 0.5);
            border-radius: 3px;
        }

        .trigger-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 4px solid #2ecc71;
            transition: all 0.3s ease;
            position: relative;
        }

        .trigger-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.2);
        }

        .trigger-time {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .trigger-condition {
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .trigger-action {
            font-size: 13px;
            color: #34495e;
            margin-bottom: 8px;
            font-style: italic;
        }

        .trigger-result {
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }

        .trigger-result.success {
            background: rgba(46, 204, 113, 0.2);
            color: #27ae60;
        }

        .trigger-result.warning {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
        }

        .trigger-result.error {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        /* 参数编辑样式 */
        .param-edit-form {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
            border: 2px solid #3498db;
        }

        .param-edit-form.active {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .param-input-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
        }

        .param-input {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .param-input label {
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
        }

        .param-input input, .param-input select {
            padding: 8px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 13px;
            transition: border-color 0.3s ease;
            background: white;
        }

        .param-input input:focus, .param-input select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .param-input select {
            cursor: pointer;
        }

        /* 开仓模式描述样式 */
        .mode-description {
            margin-top: 6px;
            padding: 8px;
            background: rgba(52, 152, 219, 0.1);
            border-left: 3px solid #3498db;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.3;
            color: #2c3e50;
        }

        .mode-description strong {
            color: #2980b9;
            font-weight: 600;
        }

        .mode-description .risk-level {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
        }

        .risk-low { background: #d5f4e6; color: #27ae60; }
        .risk-medium { background: #fef9e7; color: #f39c12; }
        .risk-high { background: #fadbd8; color: #e74c3c; }

        .mode-features {
            margin-top: 4px;
            font-size: 11px;
            color: #7f8c8d;
        }

        .mode-features ul {
            margin: 2px 0 0 12px;
            padding: 0;
        }

        .mode-features li {
            margin-bottom: 1px;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            margin: 2% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalSlideIn 0.3s ease;
            cursor: move;
        }

        .modal-content::-webkit-scrollbar {
            width: 8px;
        }

        .modal-content::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .modal-content::-webkit-scrollbar-thumb {
            background: rgba(52, 152, 219, 0.5);
            border-radius: 4px;
        }

        .modal-content::-webkit-scrollbar-thumb:hover {
            background: rgba(52, 152, 219, 0.7);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-content .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            color: #aaa;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .modal-content .close:hover {
            color: #e74c3c;
        }

        .modal-content h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
            font-weight: 700;
            cursor: move;
            user-select: none;
            padding: 10px 0;
            border-bottom: 2px solid #ecf0f1;
            position: relative;
        }

        .modal-content h3::after {
            content: '⋮⋮';
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            color: #bdc3c7;
            font-size: 16px;
            letter-spacing: 2px;
        }

        .modal-content h3:hover::after {
            color: #3498db;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        .button-group .btn {
            min-width: 100px;
        }

        /* 回测结果样式 */
        .backtest-results {
            margin-top: 25px;
            padding: 20px;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 16px;
            border-left: 4px solid #3498db;
        }

        .backtest-results h4 {
            color: #2980b9;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .chart-container {
            height: 300px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
        }

        /* 状态指示器样式 */
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-running {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
        }
        .status-stopped {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
        }
        .status-paused {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .panel, .header, .info-item, .metric-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .panel:nth-child(2) { animation-delay: 0.1s; }
        .panel:nth-child(3) { animation-delay: 0.2s; }
        .info-item:nth-child(2) { animation-delay: 0.1s; }
        .info-item:nth-child(3) { animation-delay: 0.2s; }
        .info-item:nth-child(4) { animation-delay: 0.3s; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                padding: 15px;
            }

            .main-content {
                gap: 20px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .strategy-info {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .control-buttons {
                flex-direction: column;
                gap: 10px;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .header h1 {
                font-size: 24px;
            }

            .panel {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .strategy-info {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .header {
                padding: 20px;
            }

            .info-item, .metric-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>
                📊 策略监控详情
                <a href="/" class="back-btn">← 返回主页</a>
            </h1>
            
            <!-- 策略基本信息 -->
            <div class="strategy-info">
                <div class="info-item">
                    <div class="info-label">策略名称</div>
                    <div class="info-value" id="strategy-name">加载中...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">运行状态</div>
                    <div class="info-value" id="strategy-status">
                        <span class="status-indicator status-stopped"></span>
                        已停止
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">运行时长</div>
                    <div class="info-value" id="strategy-uptime">00:00:00</div>
                </div>
                <div class="info-item">
                    <div class="info-label">风险等级</div>
                    <div class="info-value" id="strategy-risk">中等</div>
                </div>
                <div class="info-item">
                    <div class="info-label">交易合约</div>
                    <div class="info-value" id="strategy-symbol">au2507</div>
                </div>
                <div class="info-item">
                    <div class="info-label">最大持仓</div>
                    <div class="info-value" id="strategy-max-pos">1手</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 策略控制面板 -->
            <div class="panel">
                <h2>🎮 策略控制</h2>
                
                <div class="control-buttons">
                    <button class="btn btn-start" id="btn-start">
                        ▶️ 启动策略
                    </button>
                    <button class="btn btn-pause" id="btn-pause" disabled>
                        ⏸️ 暂停策略
                    </button>
                    <button class="btn btn-stop" id="btn-stop" disabled>
                        ⏹️ 停止策略
                    </button>
                </div>

                <!-- 紧急控制面板 -->
                <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">🚨 紧急控制</h4>
                    <div class="control-buttons">
                        <button class="btn btn-danger" id="btn-emergency-halt">
                            ⏸️ 紧急暂停
                        </button>
                        <button class="btn btn-danger" id="btn-emergency-close">
                            🔴 紧急平仓
                        </button>
                        <button class="btn btn-warning" id="btn-emergency-settings">
                            ⚠️ 紧急设置
                        </button>
                    </div>
                </div>

                <!-- 策略参数 -->
                <div style="margin-top: 25px; padding: 20px; background: rgba(52, 152, 219, 0.1); border-radius: 16px; border-left: 4px solid #3498db;">
                    <h3 style="color: #2980b9; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        ⚙️ 策略参数
                        <button class="btn btn-info btn-sm" id="btn-edit-params" style="margin-left: auto; padding: 8px 16px; font-size: 12px;">
                            编辑参数
                        </button>
                        <button class="btn btn-success btn-sm" id="btn-backtest" style="padding: 8px 16px; font-size: 12px;">
                            历史回测
                        </button>
                    </h3>
                    <div class="strategy-info">
                        <div class="info-item">
                            <div class="info-label">短期均线</div>
                            <div class="info-value" id="param-ma-short">5</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">长期均线</div>
                            <div class="info-value" id="param-ma-long">20</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">RSI周期</div>
                            <div class="info-value" id="param-rsi-period">14</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">超买阈值</div>
                            <div class="info-value" id="param-rsi-overbought">70</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">止损比例</div>
                            <div class="info-value" id="param-stop-loss">0.05</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">止盈比例</div>
                            <div class="info-value" id="param-take-profit">0.08</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">开仓手数</div>
                            <div class="info-value" id="param-position-size">1</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最大持仓</div>
                            <div class="info-value" id="param-max-position">5</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">风险系数</div>
                            <div class="info-value" id="param-risk-factor">0.02</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">加仓间隔</div>
                            <div class="info-value" id="param-add-interval">50</div>
                        </div>
                        <div class="info-item" style="grid-column: span 2;">
                            <div class="info-label">开仓模式</div>
                            <div class="info-value" id="param-position-mode">固定手数</div>
                            <div class="mode-description" id="current-mode-description" style="margin-top: 8px;">
                                <strong>固定手数:</strong> 每次开仓使用固定的手数，简单稳定，适合新手和保守策略。
                                <span class="risk-level risk-low">低风险</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">仓位倍数</div>
                            <div class="info-value" id="param-position-multiplier">1.0</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时指标 -->
            <div class="panel">
                <h2>📈 实时指标</h2>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value positive" id="total-pnl">+0.00</div>
                        <div class="metric-label">总盈亏</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value neutral" id="current-pos">0</div>
                        <div class="metric-label">当前持仓</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value neutral" id="trade-count">0</div>
                        <div class="metric-label">交易次数</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value neutral" id="win-rate">0%</div>
                        <div class="metric-label">胜率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value neutral" id="max-drawdown">0.00</div>
                        <div class="metric-label">最大回撤</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value neutral" id="sharpe-ratio">0.00</div>
                        <div class="metric-label">夏普比率</div>
                    </div>
                </div>

                <!-- 策略触发记录 -->
                <div style="margin-top: 25px; padding: 20px; background: rgba(46, 204, 113, 0.1); border-radius: 16px; border-left: 4px solid #2ecc71;">
                    <h3 style="color: #27ae60; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        🎯 策略触发记录
                        <button class="btn btn-secondary btn-sm" id="btn-clear-triggers" style="margin-left: auto; padding: 6px 12px; font-size: 12px;">
                            清空记录
                        </button>
                    </h3>
                    <div id="trigger-records" class="trigger-container">
                        <div class="trigger-item">
                            <div class="trigger-time">2024-01-15 14:30:25</div>
                            <div class="trigger-condition">
                                <strong>买入信号:</strong> MA5(435.2) > MA20(434.8), RSI(25.6) < 30
                            </div>
                            <div class="trigger-action">
                                执行操作: 买入 au2406 1手 @ 435.5 (开仓手数: 1, 风险系数: 0.02)
                            </div>
                            <div class="trigger-result success">✅ 订单已成交</div>
                        </div>
                        <div class="trigger-item">
                            <div class="trigger-time">2024-01-15 14:25:10</div>
                            <div class="trigger-condition">
                                <strong>卖出信号:</strong> MA5(434.1) < MA20(434.5), RSI(75.2) > 70
                            </div>
                            <div class="trigger-action">
                                执行操作: 卖出 au2406 1手 @ 434.0 (平仓操作, 止盈: 8%)
                            </div>
                            <div class="trigger-result success">✅ 订单已成交</div>
                        </div>
                        <div class="trigger-item">
                            <div class="trigger-time">2024-01-15 14:20:05</div>
                            <div class="trigger-condition">
                                <strong>止损信号:</strong> 价格(433.2) < 止损价(433.5)
                            </div>
                            <div class="trigger-action">
                                执行操作: 止损平仓 au2406 1手 @ 433.2 (止损: 5%, 亏损: -0.3点)
                            </div>
                            <div class="trigger-result warning">⚠️ 止损执行</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 持仓和订单信息 -->
        <div class="main-content">
            <div class="panel">
                <h2>📋 当前持仓</h2>
                <table class="positions-table">
                    <thead>
                        <tr>
                            <th>合约</th>
                            <th>方向</th>
                            <th>数量</th>
                            <th>开仓价</th>
                            <th>当前价</th>
                            <th>盈亏</th>
                        </tr>
                    </thead>
                    <tbody id="positions-tbody">
                        <tr>
                            <td colspan="6" style="text-align: center; color: #666;">暂无持仓</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="panel">
                <h2>📝 活跃订单</h2>
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>合约</th>
                            <th>方向</th>
                            <th>数量</th>
                            <th>价格</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="orders-tbody">
                        <tr>
                            <td colspan="6" style="text-align: center; color: #666;">暂无活跃订单</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 策略日志 -->
        <div class="panel full-width">
            <h2>📋 策略日志</h2>
            <div class="log-container" id="strategy-logs">
                <div class="log-entry">
                    <span class="log-timestamp">2025-07-09 17:45:00</span>
                    <span class="log-level-INFO">[INFO]</span>
                    策略监控页面已加载，等待策略启动...
                </div>
            </div>
        </div>
    </div>

    <!-- 参数编辑模态框 -->
    <div id="paramEditModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <span class="close" onclick="closeParamModal()">&times;</span>
            <h3>⚙️ 编辑策略参数</h3>
            <form id="paramEditForm">
                <div class="param-input-group">
                    <div class="param-input">
                        <label for="edit-ma-short">短期均线周期</label>
                        <input type="number" id="edit-ma-short" min="1" max="50" value="5">
                    </div>
                    <div class="param-input">
                        <label for="edit-ma-long">长期均线周期</label>
                        <input type="number" id="edit-ma-long" min="10" max="200" value="20">
                    </div>
                    <div class="param-input">
                        <label for="edit-rsi-period">RSI周期</label>
                        <input type="number" id="edit-rsi-period" min="5" max="50" value="14">
                    </div>
                    <div class="param-input">
                        <label for="edit-rsi-overbought">RSI超买阈值</label>
                        <input type="number" id="edit-rsi-overbought" min="50" max="90" value="70">
                    </div>
                    <div class="param-input">
                        <label for="edit-stop-loss">止损比例 (%)</label>
                        <input type="number" id="edit-stop-loss" min="1" max="20" step="0.1" value="5">
                    </div>
                    <div class="param-input">
                        <label for="edit-take-profit">止盈比例 (%)</label>
                        <input type="number" id="edit-take-profit" min="1" max="50" step="0.1" value="8">
                    </div>
                    <div class="param-input">
                        <label for="edit-position-size">开仓手数</label>
                        <input type="number" id="edit-position-size" min="1" max="20" value="1">
                    </div>
                    <div class="param-input">
                        <label for="edit-max-position">最大持仓手数</label>
                        <input type="number" id="edit-max-position" min="1" max="50" value="5">
                    </div>
                    <div class="param-input">
                        <label for="edit-risk-factor">风险系数</label>
                        <input type="number" id="edit-risk-factor" min="0.01" max="0.1" step="0.01" value="0.02">
                        <small style="color: #7f8c8d; font-size: 12px; margin-top: 4px; display: block;">
                            单笔交易风险占总资金的比例，0.02表示2%，建议不超过5%
                        </small>
                    </div>
                    <div class="param-input">
                        <label for="edit-add-interval">加仓间隔 (点)</label>
                        <input type="number" id="edit-add-interval" min="10" max="200" value="50">
                        <small style="color: #7f8c8d; font-size: 12px; margin-top: 4px; display: block;">
                            价格变动多少点后允许加仓，防止频繁加仓，建议根据品种波动性设置
                        </small>
                    </div>
                    <div class="param-input">
                        <label for="edit-position-mode">开仓模式</label>
                        <div style="display: flex; gap: 8px; align-items: flex-end;">
                            <select id="edit-position-mode" onchange="updatePositionModeDescription()" style="flex: 1;">
                                <option value="fixed">固定手数</option>
                                <option value="risk_based">风险比例</option>
                                <option value="kelly">凯利公式</option>
                                <option value="martingale">马丁格尔</option>
                            </select>
                            <button type="button" onclick="testPositionMode()" style="
                                padding: 8px 12px;
                                background: linear-gradient(135deg, #3498db, #2980b9);
                                color: white;
                                border: none;
                                border-radius: 4px;
                                font-size: 11px;
                                cursor: pointer;
                                white-space: nowrap;
                            ">🧮 测试</button>
                        </div>
                        <div id="position-mode-description" class="mode-description">
                            <strong>固定手数:</strong> 每次开仓使用固定的手数，简单稳定，适合新手和保守策略。
                        </div>
                    </div>
                    <div class="param-input">
                        <label for="edit-position-multiplier">仓位倍数</label>
                        <input type="number" id="edit-position-multiplier" min="0.1" max="5" step="0.1" value="1.0">
                        <small style="color: #7f8c8d; font-size: 12px; margin-top: 4px; display: block;">
                            调整最终仓位大小的倍数，1.0为标准，大于1.0增加仓位，小于1.0减少仓位
                        </small>
                    </div>
                </div>

                <!-- 参数说明区域 -->
                <div style="margin-top: 15px; padding: 10px; background: rgba(52, 152, 219, 0.05); border-radius: 8px; border-left: 3px solid #3498db;">
                    <h4 style="color: #2980b9; margin-bottom: 8px; font-size: 13px; cursor: pointer; user-select: none;" onclick="toggleParamHelp()">
                        💡 参数说明 <span id="param-help-toggle">▼</span>
                    </h4>
                    <div id="param-help-content" style="font-size: 12px; color: #2c3e50; line-height: 1.4;">
                        <p style="margin: 4px 0;"><strong>技术指标:</strong> 影响买卖信号生成，建议根据回测调整。</p>
                        <p style="margin: 4px 0;"><strong>风险控制:</strong> 止损止盈比例影响风险收益比。</p>
                        <p style="margin: 4px 0;"><strong>开仓参数:</strong> 控制仓位大小，建议小仓位测试。</p>
                        <p style="margin: 4px 0;"><strong>⚠️ 提醒:</strong> 修改后建议先回测验证效果。</p>
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" class="btn btn-success" onclick="saveStrategyParams()">保存参数</button>
                    <button type="button" class="btn btn-secondary" onclick="closeParamModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 回测模态框 -->
    <div id="backtestModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <span class="close" onclick="closeBacktestModal()">&times;</span>
            <h3>📊 策略历史回测</h3>
            <div class="backtest-form">
                <div class="param-input-group">
                    <div class="param-input">
                        <label for="backtest-start-date">开始日期</label>
                        <input type="date" id="backtest-start-date" value="2024-01-01">
                    </div>
                    <div class="param-input">
                        <label for="backtest-end-date">结束日期</label>
                        <input type="date" id="backtest-end-date" value="2024-01-15">
                    </div>
                    <div class="param-input">
                        <label for="backtest-initial-capital">初始资金</label>
                        <input type="number" id="backtest-initial-capital" min="10000" step="1000" value="100000">
                    </div>
                    <div class="param-input">
                        <label for="backtest-commission">手续费率 (%)</label>
                        <input type="number" id="backtest-commission" min="0" max="1" step="0.01" value="0.05">
                    </div>
                </div>
                <div class="button-group">
                    <button type="button" class="btn btn-primary" onclick="startBacktest()">开始回测</button>
                    <button type="button" class="btn btn-secondary" onclick="closeBacktestModal()">取消</button>
                </div>
            </div>
            <div id="backtest-results" class="backtest-results" style="display: none;">
                <h4>📈 回测结果</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-total-return">0.00%</div>
                        <div class="metric-label">总收益率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-annual-return">0.00%</div>
                        <div class="metric-label">年化收益率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-max-drawdown">0.00%</div>
                        <div class="metric-label">最大回撤</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-sharpe">0.00</div>
                        <div class="metric-label">夏普比率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-win-rate">0.00%</div>
                        <div class="metric-label">胜率</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="backtest-trade-count">0</div>
                        <div class="metric-label">交易次数</div>
                    </div>
                </div>
                <div id="backtest-chart" style="height: 300px; margin-top: 20px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666;">
                    回测图表将在此显示
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取URL参数中的策略名称
        const urlParams = new URLSearchParams(window.location.search);
        const strategyName = urlParams.get('strategy') || 'shfe_quant';
        
        // 更新页面标题和策略名称
        document.getElementById('strategy-name').textContent = strategyName;
        document.title = `${strategyName} - 策略监控`;

        // API端点
        const API_BASE = '/api/v1';
        
        // 策略状态
        let currentStatus = 'stopped';
        let logContainer = document.getElementById('strategy-logs');
        
        // 添加日志条目
        function addLogEntry(level, message) {
            const timestamp = new Date().toLocaleString('zh-CN');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">${timestamp}</span>
                <span class="log-level-${level}">[${level}]</span>
                ${message}
            `;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 更新策略状态显示
        function updateStrategyStatus(status) {
            currentStatus = status;
            const statusElement = document.getElementById('strategy-status');
            const indicator = statusElement.querySelector('.status-indicator');
            
            // 更新按钮状态
            const startBtn = document.getElementById('btn-start');
            const pauseBtn = document.getElementById('btn-pause');
            const stopBtn = document.getElementById('btn-stop');
            
            switch(status) {
                case 'running':
                    statusElement.innerHTML = '<span class="status-indicator status-running"></span> 运行中';
                    startBtn.disabled = true;
                    pauseBtn.disabled = false;
                    stopBtn.disabled = false;
                    break;
                case 'paused':
                    statusElement.innerHTML = '<span class="status-indicator status-paused"></span> 已暂停';
                    startBtn.disabled = false;
                    pauseBtn.disabled = true;
                    stopBtn.disabled = false;
                    break;
                default:
                    statusElement.innerHTML = '<span class="status-indicator status-stopped"></span> 已停止';
                    startBtn.disabled = false;
                    pauseBtn.disabled = true;
                    stopBtn.disabled = true;
            }
        }
        
        // 策略控制函数
        async function controlStrategy(action) {
            try {
                addLogEntry('INFO', `正在${action}策略...`);
                
                const response = await fetch(`${API_BASE}/strategies/${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy_name: strategyName
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    addLogEntry('INFO', `策略${action}成功: ${result.message}`);
                    
                    // 更新状态
                    if (action === 'start') updateStrategyStatus('running');
                    else if (action === 'halt') updateStrategyStatus('paused');
                    else if (action === 'stop') updateStrategyStatus('stopped');
                    
                } else {
                    addLogEntry('ERROR', `策略${action}失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `策略${action}异常: ${error.message}`);
            }
        }
        
        // 绑定按钮事件
        document.getElementById('btn-start').addEventListener('click', () => controlStrategy('start'));
        document.getElementById('btn-pause').addEventListener('click', () => controlStrategy('halt'));
        document.getElementById('btn-stop').addEventListener('click', () => controlStrategy('stop'));

        // 绑定紧急控制按钮事件
        document.getElementById('btn-emergency-halt').addEventListener('click', () => emergencyHaltStrategy());
        document.getElementById('btn-emergency-close').addEventListener('click', () => emergencyCloseStrategy());
        document.getElementById('btn-emergency-settings').addEventListener('click', () => showEmergencySettings());

        // 绑定新功能按钮事件
        document.getElementById('btn-edit-params').addEventListener('click', () => showParamEditModal());
        document.getElementById('btn-backtest').addEventListener('click', () => showBacktestModal());
        document.getElementById('btn-clear-triggers').addEventListener('click', () => clearTriggerRecords());
        
        // 加载策略详细信息
        async function loadStrategyDetails() {
            try {
                const response = await fetch(`${API_BASE}/strategies/${strategyName}/details`);
                const result = await response.json();

                if (result.success && result.data) {
                    const strategy = result.data;

                    // 更新策略基本信息
                    document.getElementById('strategy-name').textContent = strategy.display_name;
                    updateStrategyStatus(strategy.status);

                    // 更新运行时长
                    document.getElementById('strategy-uptime').textContent = strategy.runtime || '00:00:00';

                    // 更新实时指标
                    if (strategy.statistics) {
                        const stats = strategy.statistics;
                        document.getElementById('total-pnl').textContent =
                            (stats.current_profit >= 0 ? '+' : '') + stats.current_profit.toFixed(2);
                        document.getElementById('total-pnl').className =
                            'metric-value ' + (stats.current_profit >= 0 ? 'positive' : 'negative');

                        document.getElementById('current-pos').textContent = '0'; // 暂时硬编码
                        document.getElementById('trade-count').textContent = stats.successful_trades + stats.failed_trades;
                        document.getElementById('win-rate').textContent = (stats.win_rate * 100).toFixed(1) + '%';
                        document.getElementById('max-drawdown').textContent = stats.max_drawdown.toFixed(2);
                        document.getElementById('sharpe-ratio').textContent = stats.sharpe_ratio.toFixed(2);
                    }

                    addLogEntry('INFO', `策略详细信息加载成功: ${strategy.display_name}`);
                } else {
                    addLogEntry('ERROR', `加载策略详细信息失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `加载策略详细信息异常: ${error.message}`);
            }
        }

        // 加载策略配置参数
        async function loadStrategyConfig() {
            try {
                // 从配置中获取策略参数（这里暂时使用默认值）
                if (strategyName === 'shfe_quant') {
                    document.getElementById('param-ma-short').textContent = '5';
                    document.getElementById('param-ma-long').textContent = '20';
                    document.getElementById('param-rsi-period').textContent = '14';
                    document.getElementById('param-rsi-overbought').textContent = '70';
                    document.getElementById('strategy-symbol').textContent = 'au2507';
                    document.getElementById('strategy-max-pos').textContent = '1手';
                    document.getElementById('strategy-risk').textContent = '高';
                }

                addLogEntry('INFO', '策略配置参数加载完成');
            } catch (error) {
                addLogEntry('ERROR', `加载策略配置失败: ${error.message}`);
            }
        }

        // 定期更新数据
        function updateData() {
            loadStrategyDetails();
        }

        // 初始化页面
        async function initPage() {
            addLogEntry('INFO', `策略监控页面初始化完成: ${strategyName}`);

            // 加载初始数据
            await loadStrategyDetails();
            await loadStrategyConfig();

            // 每10秒更新一次数据
            setInterval(updateData, 10000);
        }
        
        // 紧急控制函数
        async function emergencyHaltStrategy() {
            if (!confirm(`确认紧急暂停策略 "${strategyName}"？`)) return;

            const reason = prompt('请输入暂停原因:');
            if (!reason) return;

            try {
                addLogEntry('WARN', `正在紧急暂停策略: ${reason}`);

                const response = await fetch(`${API_BASE}/strategies/halt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy_name: strategyName,
                        reason: reason,
                        emergency: true,
                        operator: 'web_user'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    addLogEntry('INFO', `策略紧急暂停成功: ${result.message}`);
                    updateStrategyStatus('stopped');
                } else {
                    addLogEntry('ERROR', `策略紧急暂停失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `紧急暂停异常: ${error.message}`);
            }
        }

        async function emergencyCloseStrategy() {
            const confirmCode = prompt(`紧急平仓策略 "${strategyName}" 需要确认码，请输入 EMERGENCY_CONFIRM:`);
            if (confirmCode !== 'EMERGENCY_CONFIRM') {
                addLogEntry('ERROR', '确认码错误，紧急平仓取消');
                return;
            }

            const reason = prompt('请输入平仓原因:');
            if (!reason) return;

            try {
                addLogEntry('WARN', `正在紧急平仓策略: ${reason}`);

                const response = await fetch(`${API_BASE}/strategies/emergency_close`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        strategy_name: strategyName,
                        action: 'emergency_close',
                        reason: reason,
                        operator: 'web_user',
                        confirmation_code: confirmCode
                    })
                });

                const result = await response.json();

                if (result.success) {
                    addLogEntry('INFO', `策略紧急平仓成功: ${result.message}`);
                } else {
                    addLogEntry('ERROR', `策略紧急平仓失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `紧急平仓异常: ${error.message}`);
            }
        }

        function showEmergencySettings() {
            addLogEntry('INFO', '紧急设置功能开发中...');
            alert('紧急设置功能正在开发中，敬请期待！');
        }

        // 参数编辑功能
        function showParamEditModal() {
            // 加载当前参数值
            document.getElementById('edit-ma-short').value = document.getElementById('param-ma-short').textContent;
            document.getElementById('edit-ma-long').value = document.getElementById('param-ma-long').textContent;
            document.getElementById('edit-rsi-period').value = document.getElementById('param-rsi-period').textContent;
            document.getElementById('edit-rsi-overbought').value = document.getElementById('param-rsi-overbought').textContent;
            document.getElementById('edit-stop-loss').value = parseFloat(document.getElementById('param-stop-loss').textContent) * 100;
            document.getElementById('edit-take-profit').value = parseFloat(document.getElementById('param-take-profit').textContent) * 100;
            document.getElementById('edit-position-size').value = document.getElementById('param-position-size').textContent;
            document.getElementById('edit-max-position').value = document.getElementById('param-max-position').textContent;
            document.getElementById('edit-risk-factor').value = document.getElementById('param-risk-factor').textContent;
            document.getElementById('edit-add-interval').value = document.getElementById('param-add-interval').textContent;

            // 设置开仓模式选择
            const positionModeText = document.getElementById('param-position-mode').textContent;
            const positionModeMap = {
                '固定手数': 'fixed',
                '风险比例': 'risk_based',
                '凯利公式': 'kelly',
                '马丁格尔': 'martingale'
            };
            document.getElementById('edit-position-mode').value = positionModeMap[positionModeText] || 'fixed';
            document.getElementById('edit-position-multiplier').value = document.getElementById('param-position-multiplier').textContent;

            document.getElementById('paramEditModal').style.display = 'block';
        }

        function closeParamModal() {
            document.getElementById('paramEditModal').style.display = 'none';
        }

        function closeBacktestModal() {
            document.getElementById('backtestModal').style.display = 'none';
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // 关闭所有打开的模态框
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                    }
                });
            }
        });

        async function saveStrategyParams() {
            const params = {
                ma_short: parseInt(document.getElementById('edit-ma-short').value),
                ma_long: parseInt(document.getElementById('edit-ma-long').value),
                rsi_period: parseInt(document.getElementById('edit-rsi-period').value),
                rsi_overbought: parseInt(document.getElementById('edit-rsi-overbought').value),
                stop_loss: parseFloat(document.getElementById('edit-stop-loss').value) / 100,
                take_profit: parseFloat(document.getElementById('edit-take-profit').value) / 100,
                position_size: parseInt(document.getElementById('edit-position-size').value),
                max_position: parseInt(document.getElementById('edit-max-position').value),
                risk_factor: parseFloat(document.getElementById('edit-risk-factor').value),
                add_interval: parseInt(document.getElementById('edit-add-interval').value),
                position_mode: document.getElementById('edit-position-mode').value,
                position_multiplier: parseFloat(document.getElementById('edit-position-multiplier').value)
            };

            try {
                addLogEntry('INFO', '正在保存策略参数...');

                const response = await fetch(`${API_BASE}/strategies/${strategyName}/params`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(params)
                });

                const result = await response.json();

                if (result.success) {
                    addLogEntry('INFO', '策略参数保存成功');
                    // 更新页面显示
                    document.getElementById('param-ma-short').textContent = params.ma_short;
                    document.getElementById('param-ma-long').textContent = params.ma_long;
                    document.getElementById('param-rsi-period').textContent = params.rsi_period;
                    document.getElementById('param-rsi-overbought').textContent = params.rsi_overbought;
                    document.getElementById('param-stop-loss').textContent = params.stop_loss.toFixed(3);
                    document.getElementById('param-take-profit').textContent = params.take_profit.toFixed(3);
                    document.getElementById('param-position-size').textContent = params.position_size;
                    document.getElementById('param-max-position').textContent = params.max_position;
                    document.getElementById('param-risk-factor').textContent = params.risk_factor.toFixed(3);
                    document.getElementById('param-add-interval').textContent = params.add_interval;

                    // 更新开仓模式显示
                    const positionModeTextMap = {
                        'fixed': '固定手数',
                        'risk_based': '风险比例',
                        'kelly': '凯利公式',
                        'martingale': '马丁格尔'
                    };
                    document.getElementById('param-position-mode').textContent = positionModeTextMap[params.position_mode] || '固定手数';
                    document.getElementById('param-position-multiplier').textContent = params.position_multiplier.toFixed(1);

                    // 更新当前模式描述
                    updateCurrentModeDescription(params.position_mode);

                    closeParamModal();
                } else {
                    addLogEntry('ERROR', `参数保存失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `参数保存异常: ${error.message}`);
            }
        }

        // 回测功能
        function showBacktestModal() {
            // 设置默认日期
            const today = new Date();
            const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            document.getElementById('backtest-end-date').value = today.toISOString().split('T')[0];
            document.getElementById('backtest-start-date').value = oneMonthAgo.toISOString().split('T')[0];

            document.getElementById('backtestModal').style.display = 'block';
            document.getElementById('backtest-results').style.display = 'none';
        }

        function closeBacktestModal() {
            document.getElementById('backtestModal').style.display = 'none';
        }

        async function startBacktest() {
            const backtestParams = {
                strategy_name: strategyName,
                start_date: document.getElementById('backtest-start-date').value,
                end_date: document.getElementById('backtest-end-date').value,
                initial_capital: parseFloat(document.getElementById('backtest-initial-capital').value),
                commission_rate: parseFloat(document.getElementById('backtest-commission').value) / 100
            };

            try {
                addLogEntry('INFO', '正在启动历史回测...');

                const response = await fetch(`${API_BASE}/strategies/${strategyName}/backtest`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(backtestParams)
                });

                const result = await response.json();

                if (result.success) {
                    addLogEntry('INFO', '回测完成，正在显示结果...');
                    displayBacktestResults(result.data);
                } else {
                    addLogEntry('ERROR', `回测失败: ${result.message}`);
                }
            } catch (error) {
                addLogEntry('ERROR', `回测异常: ${error.message}`);
            }
        }

        function displayBacktestResults(results) {
            // 显示回测结果
            document.getElementById('backtest-total-return').textContent = `${(results.total_return * 100).toFixed(2)}%`;
            document.getElementById('backtest-annual-return').textContent = `${(results.annual_return * 100).toFixed(2)}%`;
            document.getElementById('backtest-max-drawdown').textContent = `${(results.max_drawdown * 100).toFixed(2)}%`;
            document.getElementById('backtest-sharpe').textContent = results.sharpe_ratio.toFixed(2);
            document.getElementById('backtest-win-rate').textContent = `${(results.win_rate * 100).toFixed(2)}%`;
            document.getElementById('backtest-trade-count').textContent = results.trade_count;

            document.getElementById('backtest-results').style.display = 'block';
        }

        // 触发记录功能
        function clearTriggerRecords() {
            if (confirm('确认清空所有触发记录？')) {
                document.getElementById('trigger-records').innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">暂无触发记录</div>';
                addLogEntry('INFO', '触发记录已清空');
            }
        }

        function addTriggerRecord(time, condition, action, result) {
            const triggerContainer = document.getElementById('trigger-records');
            const triggerItem = document.createElement('div');
            triggerItem.className = 'trigger-item';

            const resultClass = result.includes('成功') ? 'success' : result.includes('止损') ? 'warning' : 'error';

            triggerItem.innerHTML = `
                <div class="trigger-time">${time}</div>
                <div class="trigger-condition"><strong>${condition}</strong></div>
                <div class="trigger-action">${action}</div>
                <div class="trigger-result ${resultClass}">${result}</div>
            `;

            // 插入到最前面
            triggerContainer.insertBefore(triggerItem, triggerContainer.firstChild);

            // 限制记录数量，保留最新的20条
            const items = triggerContainer.querySelectorAll('.trigger-item');
            if (items.length > 20) {
                triggerContainer.removeChild(items[items.length - 1]);
            }
        }

        // 测试开仓模式效果
        function testPositionMode() {
            const mode = document.getElementById('edit-position-mode').value;
            const positionSize = parseInt(document.getElementById('edit-position-size').value);
            const riskFactor = parseFloat(document.getElementById('edit-risk-factor').value);
            const multiplier = parseFloat(document.getElementById('edit-position-multiplier').value);

            // 模拟不同模式的计算结果
            let calculatedPosition = positionSize;
            let explanation = '';

            switch(mode) {
                case 'fixed':
                    calculatedPosition = positionSize * multiplier;
                    explanation = `固定手数: ${positionSize} × ${multiplier} = ${calculatedPosition}手`;
                    break;
                case 'risk_based':
                    const accountBalance = 100000; // 假设账户余额
                    const riskAmount = accountBalance * riskFactor;
                    const pricePerLot = 435 * 1000; // 假设价格和每手数量
                    const riskPerLot = pricePerLot * 0.05; // 假设5%止损
                    calculatedPosition = Math.floor(riskAmount / riskPerLot * multiplier);
                    explanation = `风险比例: 账户${accountBalance} × ${riskFactor} ÷ 每手风险${riskPerLot.toFixed(0)} × ${multiplier} = ${calculatedPosition}手`;
                    break;
                case 'kelly':
                    const winRate = 0.6;
                    const avgWin = 1.5;
                    const avgLoss = 1.0;
                    const kellyFraction = (avgWin * winRate - (1 - winRate)) / avgWin;
                    calculatedPosition = Math.floor(kellyFraction * 10 * multiplier); // 简化计算
                    explanation = `凯利公式: 胜率${winRate} × 盈亏比${avgWin} = 凯利比例${kellyFraction.toFixed(3)} → ${calculatedPosition}手`;
                    break;
                case 'martingale':
                    const consecutiveLosses = 2; // 假设连续亏损2次
                    const martingaleMultiplier = Math.pow(2, consecutiveLosses);
                    calculatedPosition = Math.floor(positionSize * martingaleMultiplier * multiplier);
                    explanation = `马丁格尔: ${positionSize} × 2^${consecutiveLosses} × ${multiplier} = ${calculatedPosition}手`;
                    break;
            }

            // 显示计算结果
            const resultDiv = document.createElement('div');
            resultDiv.style.cssText = `
                margin-top: 10px;
                padding: 10px;
                background: rgba(46, 204, 113, 0.1);
                border-left: 3px solid #2ecc71;
                border-radius: 4px;
                font-size: 12px;
                color: #27ae60;
            `;
            resultDiv.innerHTML = `
                <strong>💡 计算结果:</strong><br>
                ${explanation}<br>
                <strong>建议开仓: ${calculatedPosition}手</strong>
            `;

            // 移除之前的结果
            const existingResult = document.getElementById('position-test-result');
            if (existingResult) {
                existingResult.remove();
            }

            // 添加新结果
            resultDiv.id = 'position-test-result';
            document.getElementById('position-mode-description').parentNode.appendChild(resultDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (document.getElementById('position-test-result')) {
                    document.getElementById('position-test-result').remove();
                }
            }, 5000);
        }

        // 更新开仓模式描述
        function updatePositionModeDescription() {
            const mode = document.getElementById('edit-position-mode').value;
            const descriptionDiv = document.getElementById('position-mode-description');

            const descriptions = {
                'fixed': {
                    title: '固定手数',
                    risk: 'low',
                    description: '每次开仓使用固定的手数，简单稳定，适合新手和保守策略。',
                    features: [
                        '风险可控，易于理解',
                        '适合稳定盈利的策略',
                        '不受账户资金变化影响',
                        '适合新手交易者'
                    ]
                },
                'risk_based': {
                    title: '风险比例',
                    risk: 'medium',
                    description: '根据账户资金的固定比例来计算开仓手数，资金增长时仓位自动增加。',
                    features: [
                        '仓位随资金规模动态调整',
                        '有效控制单笔交易风险',
                        '适合长期资金增长',
                        '需要设置合理的风险比例'
                    ]
                },
                'kelly': {
                    title: '凯利公式',
                    risk: 'medium',
                    description: '基于历史胜率和盈亏比计算最优仓位，理论上能最大化长期收益。',
                    features: [
                        '理论最优仓位计算',
                        '需要准确的胜率统计',
                        '适合有历史数据的策略',
                        '可能产生较大仓位波动'
                    ]
                },
                'martingale': {
                    title: '马丁格尔',
                    risk: 'high',
                    description: '亏损后加倍下注，盈利后回到基础仓位。高风险高收益策略。',
                    features: [
                        '亏损后自动加仓',
                        '理论上必然盈利',
                        '需要充足的资金支持',
                        '极高风险，谨慎使用'
                    ]
                }
            };

            const info = descriptions[mode];
            const riskLevelText = {
                'low': '低风险',
                'medium': '中等风险',
                'high': '高风险'
            };

            descriptionDiv.innerHTML = `
                <strong>${info.title}:</strong> ${info.description}
                <span class="risk-level risk-${info.risk}">${riskLevelText[info.risk]}</span>
                <div class="mode-features">
                    <ul>
                        ${info.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // 更新当前模式描述（用于策略参数显示区域）
        function updateCurrentModeDescription(mode) {
            const descriptions = {
                'fixed': {
                    title: '固定手数',
                    risk: 'low',
                    description: '每次开仓使用固定的手数，简单稳定，适合新手和保守策略。'
                },
                'risk_based': {
                    title: '风险比例',
                    risk: 'medium',
                    description: '根据账户资金的固定比例来计算开仓手数，资金增长时仓位自动增加。'
                },
                'kelly': {
                    title: '凯利公式',
                    risk: 'medium',
                    description: '基于历史胜率和盈亏比计算最优仓位，理论上能最大化长期收益。'
                },
                'martingale': {
                    title: '马丁格尔',
                    risk: 'high',
                    description: '亏损后加倍下注，盈利后回到基础仓位。高风险高收益策略。'
                }
            };

            const info = descriptions[mode] || descriptions['fixed'];
            const riskLevelText = {
                'low': '低风险',
                'medium': '中等风险',
                'high': '高风险'
            };

            const currentDescDiv = document.getElementById('current-mode-description');
            if (currentDescDiv) {
                currentDescDiv.innerHTML = `
                    <strong>${info.title}:</strong> ${info.description}
                    <span class="risk-level risk-${info.risk}">${riskLevelText[info.risk]}</span>
                `;
            }
        }

        // 模态框拖动功能
        function makeDraggable(modal) {
            const modalContent = modal.querySelector('.modal-content');
            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            modalContent.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            function dragStart(e) {
                // 只有点击模态框头部区域才能拖动
                if (e.target.closest('.close') || e.target.closest('input') || e.target.closest('select') || e.target.closest('button')) {
                    return;
                }

                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;

                if (e.target === modalContent || e.target.closest('h3')) {
                    isDragging = true;
                    modalContent.style.cursor = 'grabbing';
                }
            }

            function drag(e) {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;

                    xOffset = currentX;
                    yOffset = currentY;

                    // 限制拖动范围
                    const rect = modalContent.getBoundingClientRect();
                    const maxX = window.innerWidth - rect.width;
                    const maxY = window.innerHeight - rect.height;

                    xOffset = Math.max(0, Math.min(maxX, xOffset));
                    yOffset = Math.max(0, Math.min(maxY, yOffset));

                    modalContent.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
                }
            }

            function dragEnd() {
                if (isDragging) {
                    isDragging = false;
                    modalContent.style.cursor = 'move';
                }
            }

            // 重置位置
            function resetPosition() {
                xOffset = 0;
                yOffset = 0;
                modalContent.style.transform = 'translate(0px, 0px)';
            }

            // 当模态框显示时重置位置
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        if (modal.style.display === 'block') {
                            resetPosition();
                        }
                    }
                });
            });

            observer.observe(modal, { attributes: true });
        }

        // 参数说明折叠功能
        function toggleParamHelp() {
            const content = document.getElementById('param-help-content');
            const toggle = document.getElementById('param-help-toggle');

            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = '▼';
            } else {
                content.style.display = 'none';
                toggle.textContent = '▶';
            }
        }

        // 加载策略参数
        async function loadStrategyParams() {
            try {
                const response = await fetch(`${API_BASE}/strategies/${strategyName}/params`);
                const result = await response.json();

                if (result.success && result.data) {
                    const params = result.data;

                    // 更新页面显示
                    document.getElementById('param-ma-short').textContent = params.ma_short || 5;
                    document.getElementById('param-ma-long').textContent = params.ma_long || 20;
                    document.getElementById('param-rsi-period').textContent = params.rsi_period || 14;
                    document.getElementById('param-rsi-overbought').textContent = params.rsi_overbought || 70;
                    document.getElementById('param-stop-loss').textContent = (params.stop_loss || 0.05).toFixed(3);
                    document.getElementById('param-take-profit').textContent = (params.take_profit || 0.08).toFixed(3);
                    document.getElementById('param-position-size').textContent = params.position_size || 1;
                    document.getElementById('param-max-position').textContent = params.max_position || 5;
                    document.getElementById('param-risk-factor').textContent = (params.risk_factor || 0.02).toFixed(3);
                    document.getElementById('param-add-interval').textContent = params.add_interval || 50;

                    // 更新开仓模式显示
                    const positionModeTextMap = {
                        'fixed': '固定手数',
                        'risk_based': '风险比例',
                        'kelly': '凯利公式',
                        'martingale': '马丁格尔'
                    };
                    document.getElementById('param-position-mode').textContent = positionModeTextMap[params.position_mode] || '固定手数';
                    document.getElementById('param-position-multiplier').textContent = (params.position_multiplier || 1.0).toFixed(1);

                    // 更新当前模式描述
                    updateCurrentModeDescription(params.position_mode || 'fixed');

                    console.log('策略参数加载成功:', params);
                }
            } catch (error) {
                console.error('加载策略参数失败:', error);
                addLogEntry('ERROR', '加载策略参数失败');
            }
        }

        // 模拟触发记录生成器
        function generateMockTriggerRecord() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');

            const mockRecords = [
                {
                    condition: '买入信号: MA5(435.8) > MA20(434.2), RSI(28.5) < 30',
                    action: '执行操作: 买入 au2507 1手 @ 435.9 (开仓手数: 1, 风险系数: 0.02)',
                    result: '✅ 订单已成交'
                },
                {
                    condition: '卖出信号: MA5(433.1) < MA20(433.8), RSI(72.3) > 70',
                    action: '执行操作: 卖出 au2507 1手 @ 433.0 (平仓操作, 止盈: 8%)',
                    result: '✅ 订单已成交'
                },
                {
                    condition: '止损信号: 价格(432.5) < 止损价(432.8)',
                    action: '执行操作: 止损平仓 au2507 1手 @ 432.5 (止损: 5%, 亏损: -0.4点)',
                    result: '⚠️ 止损执行'
                },
                {
                    condition: '加仓信号: 价格突破(436.2), 当前持仓有利',
                    action: '执行操作: 加仓买入 au2507 1手 @ 436.3 (加仓间隔: 50点)',
                    result: '✅ 加仓成功'
                }
            ];

            const record = mockRecords[Math.floor(Math.random() * mockRecords.length)];
            addTriggerRecord(timeStr, record.condition, record.action, record.result);
        }

        // 更新策略性能数据
        function updateStrategyPerformance() {
            // 模拟性能数据变化
            const baseValues = {
                totalPnL: -2860,
                todayPnL: 150,
                position: 0,
                winRate: 0.65,
                totalTrades: 28,
                maxDrawdown: 0.08
            };

            // 添加随机波动
            const variation = (Math.random() - 0.5) * 0.1; // ±5%的波动

            const newTotalPnL = baseValues.totalPnL + (Math.random() - 0.5) * 200;
            const newTodayPnL = baseValues.todayPnL + (Math.random() - 0.5) * 100;
            const newPosition = Math.floor((Math.random() - 0.5) * 6); // -3到3的持仓

            // 更新页面显示
            const totalPnLElement = document.querySelector('.metric-card:nth-child(1) .metric-value');
            const todayPnLElement = document.querySelector('.metric-card:nth-child(2) .metric-value');
            const positionElement = document.querySelector('.metric-card:nth-child(3) .metric-value');
            const winRateElement = document.querySelector('.metric-card:nth-child(4) .metric-value');

            if (totalPnLElement) {
                totalPnLElement.textContent = `¥${newTotalPnL.toFixed(2)}`;
                totalPnLElement.className = `metric-value ${newTotalPnL >= 0 ? 'positive' : 'negative'}`;
            }

            if (todayPnLElement) {
                todayPnLElement.textContent = `¥${newTodayPnL.toFixed(2)}`;
                todayPnLElement.className = `metric-value ${newTodayPnL >= 0 ? 'positive' : 'negative'}`;
            }

            if (positionElement) {
                positionElement.textContent = `${newPosition}手`;
                positionElement.className = `metric-value ${newPosition > 0 ? 'positive' : newPosition < 0 ? 'negative' : ''}`;
            }

            if (winRateElement) {
                const newWinRate = Math.max(0.4, Math.min(0.8, baseValues.winRate + variation));
                winRateElement.textContent = `${(newWinRate * 100).toFixed(1)}%`;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPage();

            // 加载策略参数
            loadStrategyParams();

            // 为所有模态框添加拖动功能
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                makeDraggable(modal);
            });

            // 每30秒生成一个模拟触发记录
            setInterval(generateMockTriggerRecord, 30000);

            // 每10秒更新策略性能数据
            setInterval(updateStrategyPerformance, 10000);
        });
    </script>
</body>
</html>
